# 🚗 IoT Car Detection System

Sistem deteksi mobil berbasis IoT menggunakan Computer Vision untuk **Bang Ormat**. Aplikasi ini dapat mendeteksi berbagai jenis kendaraan dalam gambar menggunakan teknologi AI dari Roboflow.

## ✨ Fitur Utama

- 🚗 **Deteksi Mobil Otomatis** - Mendeteksi berbagai jenis kend<PERSON>
- 📱 **Upload Gambar** - Drag & drop atau pilih file gambar
- 📷 **Kamera Real-time** - Ambil foto langsung dari kamera
- 🎯 **Bounding Box** - Menampilkan kotak deteksi pada gambar
- 📊 **Confidence Score** - Menampilkan tingkat kepercayaan deteksi
- 🌐 **Web Interface** - Interface yang user-friendly
- 🚀 **API Ready** - Siap untuk integrasi dengan sistem lain

## 🚙 Jenis Kendaraan yang Dapat Dideteksi

- 🚗 Mobil Sedan
- 🚙 SUV
- 🚐 Van
- 🚚 Truk
- 🏎️ Sports Car
- 🚕 Taxi
- 🚌 Bus
- 🚛 Truck Besar
- 🚘 Hatchback
- 🛻 Pickup

## 🛠️ Teknologi yang Digunakan

- **Backend**: Node.js + Express
- **Frontend**: HTML5, CSS3, JavaScript
- **AI Model**: Roboflow Car Detection API
- **File Upload**: Multer
- **HTTP Client**: Axios
- **Styling**: Modern CSS dengan gradients

## 📦 Instalasi

1. **Clone atau download project ini**
2. **Install dependencies:**
   ```bash
   npm install
   ```
3. **Jalankan server:**
   ```bash
   npm start
   ```
4. **Buka browser dan akses:**
   ```
   http://localhost:3000
   ```

## 🚀 Cara Penggunaan

### 1. Upload Gambar
- Klik area upload atau drag & drop gambar mobil
- Klik tombol "Deteksi Mobil"
- Lihat hasil deteksi dengan bounding box

### 2. Menggunakan Kamera
- Klik tab "Kamera"
- Izinkan akses kamera
- Klik "Ambil Foto Mobil"
- Klik "Deteksi Mobil"

### 3. API Endpoints

#### Status API
```
GET /api/status
```

#### Deteksi Mobil
```
POST /api/detect-car
Content-Type: multipart/form-data
Body: image file
```

## 🧪 Testing

Jalankan test API:
```bash
node test-car-api.js
```

## 📁 Struktur Project

```
car-detection/
├── server.js              # Server utama
├── package.json           # Dependencies
├── public/
│   └── index.html         # Frontend interface
├── uploads/               # Folder untuk file upload
├── test-car-api.js        # Test script
└── README.md             # Dokumentasi ini
```

## 🔧 Konfigurasi

API menggunakan Roboflow model:
- **Model**: mobil-obfz5/5
- **API Key**: hJyEjEPMYMvFlU7OkVGL
- **Endpoint**: https://serverless.roboflow.com/mobil-obfz5/5

## 🌐 Deploy ke Render

1. **Push code ke GitHub**
2. **Connect ke Render.com**
3. **Set environment variables** (jika diperlukan)
4. **Deploy otomatis**

## 📱 Responsive Design

- ✅ Desktop friendly
- ✅ Mobile responsive
- ✅ Tablet compatible
- ✅ Touch-friendly interface

## 🎨 Tema Warna

- **Primary**: Blue (#007bff)
- **Secondary**: Green (#28a745)
- **Background**: Blue-Green gradient
- **Accent**: Modern blue tones

## 🔒 Keamanan

- File upload validation
- Image type checking
- File size limits (10MB)
- Error handling
- CORS enabled

## 📞 Support

Dibuat khusus untuk **Bang Ormat** 🚗

**Features:**
- ✅ Car detection
- ✅ Real-time camera
- ✅ File upload
- ✅ Modern UI
- ✅ API ready
- ✅ Mobile responsive

**Tech Stack:**
- Node.js + Express
- Roboflow AI
- Modern JavaScript
- Responsive CSS

---

**Made with ❤️ for Bang Ormat**  
*Powered by Roboflow AI & Node.js*
