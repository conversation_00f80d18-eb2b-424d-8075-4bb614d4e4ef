{"name": "animal-detection-iot", "version": "1.0.0", "description": "IoT Animal Detection System using Computer Vision", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-api.js"}, "keywords": ["iot", "animal-detection", "computer-vision", "rob<PERSON>low", "nodejs"], "author": "Animal Detection Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "axios": "^1.6.0", "cors": "^2.8.5", "path": "^0.12.7", "form-data": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}}